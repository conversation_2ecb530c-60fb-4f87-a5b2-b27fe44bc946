import tomllib
import pandas as pd
from src.config import Config
from src.logger import get_logger
from src.data.yahoo_fetcher import get_stock_price_timeseries
from src.animation.time_series_animation import create_wealth_animation
from src.utils import construct_string_title


LOGGER = get_logger(__file__)
LOGGER.activate()


if __name__ == "__main__":

    # Load configuration from TOML
    LOGGER.info("Loading .toml configuration file")
    with open("config.toml", "rb") as f:
        config_data = tomllib.load(f)

    # Initialize Config with values from the TOML file
    config = Config(config_dict=config_data)

    projections = []

    # Loop through tickers
    for ticker, company_name in config.ticker.items():

        LOGGER.info(f"Processing {ticker} ...")
    
        # Fetch stock data for ticker
        stock_data = get_stock_price_timeseries(
            ticker,
            interval=config.interval,
            start_date=config.start_date_str,
            end_date=config.today_str,
            save_data=config.save_data,
        )

        # Create investment object
        investment = config.investment_object(
            price_time_series=stock_data["Close"],
            dividend_time_series=stock_data["Dividends"],
            investment_amount=config.investment_amount,
            tax_rate=config.tax_rate,
            tax_free_return_threshold_per_annu=config.tax_free_return_threshold_per_annu,
        )

        # Get wealth projection and append to list as series
        projection = investment.invest(config.dividends[ticker])
        projection_series = projection["Wealth"].rename(company_name)

        projections.append(projection_series)

    LOGGER.info("Concatenating projections ...")
    # Add total investments and concat all projections
    projections.append(projection["Total Investments"])
    projections = pd.concat(projections, axis=1)

    # Create title
    title = construct_string_title(
        list(config.ticker.values()), 
        config.investment_amount, 
        config.years_ago, 
        config.interval_title
        )

    # Create animation
    create_wealth_animation(
        projections,
        config.years_ago,
        filename="assets/animations/wealth_animation.mp4",
        duration_sec=config.animation_duration_seconds,
        fps=config.fps,
        title=title,
    )