#!/usr/bin/env python3
"""
ReelStonks Project Setup Script

This script sets up the necessary directory structure for the ReelStonks project.
Run this script after cloning the repository to ensure all required directories exist.

Usage:
    python setup_project.py
    
    or
    
    uv run setup_project.py
"""

import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.setup import setup_project, ProjectSetup
from src.logger import get_logger


def main():
    """Main setup function."""
    print("🚀 ReelStonks Project Setup")
    print("=" * 50)
    
    try:
        # Initialize logger
        logger = get_logger(__name__)
        logger.activate()
        
        # Create setup instance
        setup = ProjectSetup()
        
        # Check current status
        status = setup.get_setup_status()
        
        if status['is_setup']:
            print("✅ Project is already set up!")
            print(f"📁 Project root: {status['project_root']}")
            print("\nExisting directories:")
            for dir_path in status['existing_dirs']:
                print(f"  ✓ {dir_path.relative_to(status['project_root'])}")
        else:
            print("📋 Setting up project directories...")
            success = setup_project(verbose=True)
            
            if success:
                print("\n🎉 Project setup completed successfully!")
                print("\nYou can now run:")
                print("  python main.py")
                print("  or")
                print("  uv run main.py")
            else:
                print("\n❌ Project setup failed!")
                return 1
        
        # Optionally create .gitkeep files
        print("\n📝 Creating .gitkeep files for empty directories...")
        setup.create_gitkeep_files()
        
        print("\n✨ Setup complete! Ready to create amazing investment animations.")
        return 0
        
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
