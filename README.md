# 📈 ReelStonks

<div align="center">

**🎬 Turn Your Investment Dreams Into Cinematic Reality 🎬**

*Create stunning animated visualizations of "what if" investment scenarios*

[![Python](https://img.shields.io/badge/Python-3.13+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/Code%20Style-Black-black.svg)](https://github.com/psf/black)

</div>

---

## 🌟 What is ReelStonks?

ReelStonks is a powerful Python application that transforms historical stock market data into **mesmerizing animated visualizations**. Ever wondered *"What if I had invested $100 every month in Apple 20 years ago?"* 🤔

ReelStonks answers that question with:
- 📊 **Stunning animated charts** with glowing line effects
- 💰 **Realistic investment simulations** with tax considerations
- 🎯 **Multiple investment strategies** (daily, weekly, monthly, quarterly, yearly)
- 📈 **Performance metrics** including CAGR and volatility analysis
- 🎥 **Cinema-quality animations** ready for social media

## ✨ Features

### 🎨 **Visual Excellence**
- **Glowing line animations** with smooth easing transitions
- **Dark theme aesthetics** perfect for social media
- **Performance summary tables** that fade in dramatically
- **Customizable animation duration** and frame rates

### 💼 **Investment Analysis**
- **Multiple investment frequencies**: Daily, Weekly, Monthly, Quarterly, Yearly
- **Dividend handling**: Choose to reinvest or receive as cash
- **Tax calculations**: Realistic tax modeling with thresholds
- **Multi-asset comparison**: Compare multiple stocks simultaneously

### 🔧 **Technical Features**
- **Yahoo Finance integration** for real-time data
- **Vectorized calculations** for lightning-fast performance
- **Flexible configuration** via TOML files
- **Professional logging** with colored output
- **Cross-platform compatibility** (Windows, macOS, Linux)
- **Automatic directory setup** with smart path handling
- **Data persistence** with optional CSV export

## 🚀 Quick Start

### Prerequisites

- **Python 3.13+** 🐍
- **FFmpeg** (for video generation) 🎬

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/reelstonks.git
   cd reelstonks
   ```

2. **Install dependencies**
   ```bash
   # Using uv (recommended)
   uv sync

   # Or using pip
   pip install -e .
   ```

3. **Set up project directories**
   ```bash
   # Run the setup script to create necessary directories
   python setup_project.py

   # Or using uv
   uv run setup_project.py
   ```

4. **Install FFmpeg**
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

### 🎬 Create Your First Animation

1. **Configure your investment scenario** in `config.toml`:
   ```toml
   [ticker]
   AAPL = "Apple"
   MSFT = "Microsoft"

   [general]
   years_ago = 10
   investment_amount = 500
   investment_kind = "monthly"
   animation_duration_seconds = 15
   fps = 30
   ```

2. **Run the application**:
   ```bash
   python main.py

   # Or using uv
   uv run main.py
   ```

3. **Find your animation** in `assets/animations/wealth_animation.mp4` 🎉

> **Note**: The application automatically creates the necessary directory structure on first run, so you don't need to worry about missing folders!

## ⚙️ Configuration Guide

ReelStonks uses a `config.toml` file for all settings. Here's a complete configuration example:

```toml
# 📊 Stock Selection
[ticker]
AAPL = "Apple"
MSFT = "Microsoft"
GOOGL = "Google"
AMZN = "Amazon"

# 💰 Investment Parameters
[general]
years_ago = 20                           # How far back to analyze
investment_amount = 1000                 # Amount per investment
investment_kind = "monthly"              # daily/weekly/monthly/quarterly/yearly
animation_duration_seconds = 12          # Animation length
fps = 30                                # Frames per second
tax_rate = 0.26375                      # Tax rate on dividends
tax_free_return_threshold_per_annu = 1000 # Annual tax-free dividend limit
save_data = false                       # Save fetched data to CSV files

# 🔄 Dividend Settings
[dividends]
AAPL = false    # Don't reinvest Apple dividends
MSFT = true     # Reinvest Microsoft dividends
```

### 📈 Pre-configured Stock Collections

```toml
# 🏢 Tech Giants
[ticker_stocks]
AAPL = "Apple"
MSFT = "Microsoft"
GOOGL = "Google"
AMZN = "Amazon"
TSLA = "Tesla"

# 🪙 Cryptocurrencies
[ticker_crypto]
BTC-USD = "Bitcoin"
ETH-USD = "Ethereum"

# 📊 Market Indices
[ticker_indices]
"^GSPC" = "S&P 500"
"^IXIC" = "Nasdaq"
"^DJI" = "Dow Jones"
```

## 🎯 Investment Strategies

ReelStonks supports multiple investment frequencies:

| Strategy | Description | Use Case |
|----------|-------------|----------|
| 🗓️ **Daily** | Invest every trading day | Maximum dollar-cost averaging |
| 📅 **Weekly** | Invest once per week | Balanced frequency |
| 🗓️ **Monthly** | Invest once per month | Most common strategy |
| 📊 **Quarterly** | Invest every 3 months | Conservative approach |
| 📈 **Yearly** | Invest once per year | Long-term perspective |

## 🏗️ Project Structure

```
reelstonks/
├── 📁 src/
│   ├── 🎬 animation/          # Animation generation
│   ├── 📊 data/               # Data fetching (Yahoo Finance)
│   ├── 💼 invest/             # Investment strategy classes
│   ├── ⚙️ config.py           # Configuration management
│   ├── 📝 logger.py           # Enhanced logging
│   └── 🛠️ utils.py            # Utility functions
├── 📁 assets/animations/      # Generated videos
├── ⚙️ config.toml            # Configuration file
├── 🚀 main.py                # Application entry point
└── 📋 pyproject.toml         # Project dependencies
```

## 🎨 Animation Features

### Visual Effects
- **✨ Glowing lines** with multiple transparency layers
- **🌊 Smooth easing** with quadratic transitions
- **📊 Dynamic scaling** that adapts to data ranges
- **🎯 Performance summary** with fade-in effects

### Customization Options
- **🎬 Duration**: Control animation length
- **🖼️ Frame rate**: Adjust smoothness vs file size
- **🎨 Styling**: Dark theme optimized for social media
- **📝 Titles**: Auto-generated descriptive titles

## 🔧 Advanced Usage

### Custom Easing Functions

Add your own easing functions in `src/animation/_easing_functions.py`:

```python
def ease_in_out_cubic(x):
    """Custom cubic easing function"""
    if x < 0.5:
        return 4 * x * x * x
    return 1 - pow(-2 * x + 2, 3) / 2

EASING_FUNCTIONS = {
    "ease_in_out_quadratic": ease_in_out_quadratic,
    "ease_in_out_cubic": ease_in_out_cubic,  # Your custom function
}
```

### Programmatic Usage

```python
from src.config import Config
from src.data.yahoo_fetcher import get_stock_price_timeseries
from src.animation.time_series_animation import create_wealth_animation

# Load configuration
config = Config(config_dict=your_config)

# Fetch data and create animation
# ... (see main.py for full example)
```

## 📊 Output Metrics

Each animation includes a performance summary showing:

- 🏆 **Final Rankings** by total wealth
- 💰 **Final Wealth Values** for each investment
- 📈 **CAGR** (Compound Annual Growth Rate)
- 📊 **Volatility** measurements
- 💵 **Total Investment Amount**

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Run the formatter**: `black .`
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Yahoo Finance** for providing free financial data
- **Matplotlib** for powerful plotting capabilities
- **FFmpeg** for video generation
- **The Python community** for amazing libraries

---

<div align="center">

**Made with ❤️ for the investment community**

*Turn your "what if" into "what is" with ReelStonks* 🚀

[⭐ Star this repo](https://github.com/yourusername/reelstonks) • [🐛 Report Bug](https://github.com/yourusername/reelstonks/issues) • [💡 Request Feature](https://github.com/yourusername/reelstonks/issues)

</div>