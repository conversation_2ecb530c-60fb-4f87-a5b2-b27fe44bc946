def construct_string_title(
    company_names: list[str], investment_amount: int, years_ago: int, interval: str
):

    if len(company_names) > 2:
        ticker_str = f"{', '.join(company_names[:-1])}, and {company_names[-1]}"
    elif len(company_names) == 2:
        ticker_str = " and ".join(company_names)
    else:
        ticker_str = company_names[0]

    return f"What if you had invested ${investment_amount:,} each {interval.lower()} starting {years_ago} years ago in {ticker_str}?"
