from numba import njit, types
import numpy as np


_invest_signature = types.Tuple(
    (
        types.float64[:],
        types.float64[:],
        types.float64[:],
        types.float64[:],
        types.float64[:],
    )
)(
    types.float64[:],
    types.float64[:],
    types.float64,
    types.boolean[:],
    types.boolean[:],
    types.float64,
    types.float64,
    types.boolean,
)


@njit(_invest_signature, cache=True)
def _invest(
    price: np.ndarray,
    dividends: np.ndarray,
    investment_amount: float,
    investment_periods: np.ndarray,
    yearly_periods: np.ndarray,
    tax_rate: float,
    tax_free_return_threshold_per_annu: float,
    reinvest_dividends: bool,
) -> np.ndarray:

    n = len(price)

    invested_cash = np.zeros(n)
    number_of_stocks = np.zeros(n)
    paid_gross_dividends = np.zeros(n)
    paid_net_dividends = np.zeros(n)
    dividends_for_wealth = np.zeros(n)

    wealth = np.zeros(n)

    _dividends_per_annum = 0

    for i in range(n):

        # Get number of stocks held the previous day
        prev_stocks = number_of_stocks[i - 1] if i > 0 else 0.0

        # Check if we are in a new year
        if yearly_periods[i]:
            # Reset dividends per annum
            _dividends_per_annum = 0

        # Compute gross dividends based on dividends paid today and stocks held yesterday
        paid_gross_dividends[i] = prev_stocks * dividends[i]

        # Get dividends before and after today
        dividends_before_today = _dividends_per_annum
        dividends_after_today = dividends_before_today + paid_gross_dividends[i]

        # Define taxable amount if dividends after today exceed the threshold
        taxable_amount = 0.0
        if dividends_after_today > tax_free_return_threshold_per_annu:
            # Calculate the portion of this dividend that is over the threshold
            taxable_amount = dividends_after_today - max(
                dividends_before_today, tax_free_return_threshold_per_annu
            )

        # Calculate tax paid today on the taxable amount
        tax_paid = taxable_amount * tax_rate
        # Calculate net dividends after tax
        paid_net_dividends[i] = paid_gross_dividends[i] - tax_paid
        # Update dividends per annum
        _dividends_per_annum = dividends_after_today

        if reinvest_dividends:
            # Update investment amount with reinvested dividends
            _investment_amount = investment_amount + paid_net_dividends[i]
            # Dividends are reinvested, so we don't add them to wealth
            dividends_for_wealth[i] = 0
        else:
            # No reinvestment, so investment amount stays the same
            _investment_amount = investment_amount
            # Dividends are not reinvested, so we add them to wealth
            dividends_for_wealth[i] = paid_net_dividends[i]

        # Check if we are on an investment day
        if investment_periods[i]:
            # Store investment amount at investment date
            invested_cash[i] = _investment_amount
            # Compute number of stocks bought on investment days and add to previous stocks
            number_of_stocks[i] = _investment_amount / price[i] + prev_stocks
        else:
            # Not an investment day, so no new stocks bought
            number_of_stocks[i] = prev_stocks

        # Compute wealth as of today
        wealth[i] = (number_of_stocks[i] * price[i]) + dividends_for_wealth[i]

    return (
        wealth,
        invested_cash,
        number_of_stocks,
        paid_gross_dividends,
        paid_net_dividends,
    )
