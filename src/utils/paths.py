"""Cross-platform path utilities for ReelStonks project.

This module provides utilities for managing project paths in a cross-platform
manner, ensuring compatibility across Windows, macOS, and Linux systems.
"""

import os
from pathlib import Path
from typing import Union


def get_project_root() -> Path:
    """Get the project root directory.
    
    Finds the project root by looking for the pyproject.toml file,
    starting from the current file's location and walking up the directory tree.
    
    Returns:
        Path: The project root directory path.
        
    Raises:
        FileNotFoundError: If pyproject.toml cannot be found in any parent directory.
    """
    current_path = Path(__file__).resolve()
    
    # Walk up the directory tree looking for pyproject.toml
    for parent in current_path.parents:
        if (parent / "pyproject.toml").exists():
            return parent
    
    # If not found, raise an error
    raise FileNotFoundError(
        "Could not find project root. Make sure pyproject.toml exists in the project root."
    )


def get_assets_dir() -> Path:
    """Get the assets directory path.
    
    Returns:
        Path: The assets directory path (project_root/assets).
    """
    return get_project_root() / "assets"


def get_animations_dir() -> Path:
    """Get the animations directory path.
    
    Returns:
        Path: The animations directory path (project_root/assets/animations).
    """
    return get_assets_dir() / "animations"


def get_data_dir() -> Path:
    """Get the data directory path.
    
    Returns:
        Path: The data directory path (project_root/assets/data).
    """
    return get_assets_dir() / "data"


def get_config_path() -> Path:
    """Get the config.toml file path.
    
    Returns:
        Path: The config.toml file path (project_root/config.toml).
    """
    return get_project_root() / "config.toml"


def ensure_dir_exists(path: Union[str, Path]) -> Path:
    """Ensure a directory exists, creating it if necessary.
    
    Args:
        path (Union[str, Path]): The directory path to ensure exists.
        
    Returns:
        Path: The directory path as a Path object.
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_safe_filename(ticker: str, start_date: str, end_date: str, 
                     interval: str, period: str = None) -> str:
    """Generate a safe filename for saving data files.
    
    Creates a filename that's safe across different operating systems
    by replacing or removing problematic characters.
    
    Args:
        ticker (str): The stock ticker symbol.
        start_date (str): The start date string.
        end_date (str): The end date string.
        interval (str): The data interval.
        period (str, optional): The period string. Defaults to None.
        
    Returns:
        str: A safe filename string.
    """
    # Replace problematic characters for cross-platform compatibility
    safe_ticker = ticker.replace("^", "INDEX_").replace("/", "_")
    safe_start = start_date.replace("-", "")
    safe_end = end_date.replace("-", "")
    safe_interval = interval.replace(":", "")
    
    if period:
        safe_period = period.replace(":", "")
        return f"{safe_ticker}_{safe_start}_{safe_end}_{safe_interval}_{safe_period}.csv"
    else:
        return f"{safe_ticker}_{safe_start}_{safe_end}_{safe_interval}.csv"
