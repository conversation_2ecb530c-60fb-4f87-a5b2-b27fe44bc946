from abc import ABC, abstractmethod
import pandas as pd
from src.logger import get_logger


LOGGER = get_logger(__name__)


class AbstractInvest(ABC):
    """An abstract base class for investment strategies. The project method is implemented here as
    its logic is generic, relying on the _get_investment_mask from child classes.
    """

    def __init__(
        self,
        price_time_series: pd.Series,
        dividend_time_series: pd.Series,
        investment_amount: float,
        tax_rate: float = 0.0,
        tax_free_return_threshold_per_annu: float = 0.0,
    ):
        """Initialize the AbstractInvest instance.

        Sets up the investment strategy with price and dividend data, investment
        parameters, and tax settings. Initializes result series for tracking
        investments, stock purchases, dividends, and wealth over time.

        Args:
            price_time_series (pd.Series): Time series of stock prices indexed by date.
            dividend_time_series (pd.Series): Time series of dividend payments per share
                indexed by date.
            investment_amount (float): The amount to invest at each investment interval.
            tax_rate (float, optional): Tax rate applied to dividends above the
                tax-free threshold. Defaults to 0.0.
            tax_free_return_threshold_per_annu (float, optional): Annual dividend
                amount that is tax-free. Dividends above this threshold are taxed.
                Defaults to 0.0.
        """
        LOGGER.activate()
        self.price_time_series = price_time_series
        self.dividend_time_series = dividend_time_series
        self.investment_amount = investment_amount
        self.tax_rate = tax_rate
        self.tax_free_return_threshold_per_annu = tax_free_return_threshold_per_annu
        # Initialize results Series
        self.invested_cash = pd.Series(0.0, index=self.price_time_series.index)
        self.number_of_stocks = pd.Series(0.0, index=self.price_time_series.index)
        self.paid_dividends = pd.Series(0.0, index=self.price_time_series.index)
        self.wealth = pd.Series(0.0, index=self.price_time_series.index)

        LOGGER.info(f" --- Total dividends per stock: {dividend_time_series.sum():,.1f}")
        LOGGER.info(f" --- Starting stock price: ${price_time_series.iloc[0]:,.1f}")
        LOGGER.info(f" --- Latest stock price: {price_time_series.iloc[-1]:,.1f}")

    @abstractmethod
    def _get_investment_mask(self):
        """Get the boolean mask that determines the investment days.

        This abstract method must be implemented by subclasses to define
        which days investments should be made based on the investment strategy
        (daily, weekly, monthly, quarterly, or yearly).

        Returns:
            pd.Series: Boolean series indexed by dates, where True indicates
                an investment should be made on that date.
        """
        pass

    @staticmethod
    def _apply_taxes_on_dividends_vectorized(
        dividends: pd.Series, tax_rate: float, tax_free_return_threshold_per_annu: float
    ) -> pd.Series:
        """Apply taxes to dividend payments using vectorized operations.

        Calculates net dividends after applying taxes based on annual tax-free
        thresholds. Dividends up to the threshold are tax-free, while amounts
        above the threshold are taxed at the specified rate.

        Args:
            dividends (pd.Series): Time series of gross dividend payments indexed by date.
            tax_rate (float): Tax rate to apply to dividends above the tax-free threshold.
            tax_free_return_threshold_per_annu (float): Annual dividend amount that
                is exempt from taxes.

        Returns:
            pd.Series: Time series of net dividend payments after taxes, indexed by date.
        """

        # Step 1: Cumulative dividends per year
        dividends_per_annum = dividends.groupby(dividends.index.year).cumsum()
        # Step 2: Taxed cumulative dividends
        dividends_per_annum_taxed = dividends_per_annum.mask(
            dividends_per_annum > tax_free_return_threshold_per_annu,
            tax_free_return_threshold_per_annu
            + (dividends_per_annum - tax_free_return_threshold_per_annu)
            * (1 - tax_rate),
        )
        # Step 3: Compute incremental tax per day
        tax_paid_cum = dividends_per_annum - dividends_per_annum_taxed
        tax_paid_daily = tax_paid_cum.diff().fillna(tax_paid_cum)

        # Step 4: Compute net dividends
        return dividends - tax_paid_daily

    def project(self) -> pd.DataFrame:
        """Project wealth accumulation without reinvesting dividends.

        Calculates investment performance using a fast, vectorized approach.
        Dividends are received as cash but not reinvested in additional shares.
        The calculation includes tax effects on dividends based on annual thresholds.

        Returns:
            pd.DataFrame: DataFrame with columns:
                - Investment Date: Dates when investments were made
                - Stock Price: Stock price on investment dates
                - Total Investments: Cumulative cash invested
                - Stocks Bought: Number of shares purchased on each date
                - Dividends Paid: Gross dividends received on each date
                - Cumulative Stocks: Total shares owned
                - Cumulative Dividends: Total net dividends received after taxes
                - Wealth: Total portfolio value (stocks + dividends)
        """
        LOGGER.info(f" --- Calculating projection for {self.__class__.__name__} (not reinvested dividends).")

        investment_mask = self._get_investment_mask()

        # Assign investmen amount to the days we invest
        self.invested_cash[investment_mask] = self.investment_amount
        invested_cash_cumsum = self.invested_cash.cumsum()

        # Compute number of stocks bought on investment days
        self.number_of_stocks[investment_mask] = (
            self.investment_amount / self.price_time_series[investment_mask]
        )
        cumulative_stocks = self.number_of_stocks.cumsum()

        # Get number of stocks held the previous day
        stocks_held_previous = cumulative_stocks.shift(1).fillna(0)

        # Compute dividends paid on ex-dividend date, based on stocks held on previous day
        paid_dividends = stocks_held_previous * self.dividend_time_series
        # Get paid net dividends per year applying taxes based on tax free return threshold
        net_paid_dividends = self._apply_taxes_on_dividends_vectorized(
            paid_dividends,
            self.tax_rate,
            self.tax_free_return_threshold_per_annu,
        )
        cumulative_dividends = net_paid_dividends.cumsum()

        wealth = (cumulative_stocks * self.price_time_series) + cumulative_dividends

        # Return a detailed DataFrame, filtered to only show investment days.
        return pd.DataFrame(
            {
                "Investment Date": self.price_time_series.index[investment_mask],
                "Stock Price": self.price_time_series[investment_mask],
                "Total Investments": invested_cash_cumsum[investment_mask],
                "Stocks Bought": self.number_of_stocks[investment_mask],
                "Dividends Paid": paid_dividends[investment_mask],
                "Cumulative Stocks": cumulative_stocks[investment_mask],
                "Cumulative Dividends": cumulative_dividends[investment_mask],
                "Wealth": wealth[investment_mask],
            }
        )

    def project_with_reinvested_dividends(self) -> pd.DataFrame:
        """Project wealth accumulation with dividend reinvestment.

        Calculates investment performance using an iterative approach where
        dividends are automatically reinvested to purchase additional shares.
        This method provides more accurate results for dividend reinvestment
        scenarios but is computationally slower than the vectorized approach.

        Note: This method is currently incomplete and under development.

        Returns:
            pd.DataFrame: DataFrame with investment performance data including
                reinvested dividends. Structure matches the project() method.
        """
        LOGGER.info(f" --- Calculating projection for {self.__class__.__name__} (reinvested dividends).")

        investment_mask = self._get_investment_mask()

        _df_prev = None
        _received_dividends_per_annum = 0

        for dt, price in self.price_time_series.items():

            # Reset received dividends per annum if we are in a new year
            if dt.year != _df_prev.year:
                _received_dividends_per_annum = 0

            # Get number of stocks held the previous day
            stocks_held_previous = self.number_of_stocks.get(_df_prev, 0)
            
            # Buy stocks on investment days
            if investment_mask.loc[dt]:
                self.invested_cash.loc[dt] = self.investment_amount

                # Determine gross dividends 
                gross_dividends = stocks_held_previous * self.dividend_time_series.loc[dt]

                # Calculate total received dividends per annum
                _received_dividends_per_annum += gross_dividends

                # For _received_dividends_per_annum
                # 900   => dividend paid should be 300
                # 1200  => dividend paid should be 100 + 200 * (1 - tax_rate)
                # 1500  => dividend paid should be 300 * (1 - tax_rate)
                # 1800  => dividend paid should be 300 * (1 - tax_rate)
                # ...

                # If we have reached the tax free return threshold, apply taxes
                if _received_dividends_per_annum >= self.tax_free_return_threshold_per_annu:
                    pass
                else:
                    net_dividends = gross_dividends
        
                # Compute dividends paid on ex-dividend date, based on stocks held on previous day
                self.paid_dividends.loc[dt] = stocks_held_previous * self.dividend_time_series.loc[dt]

                # Compute number of stocks bought on investment days with reinvested dividends
                self.number_of_stocks.loc[dt] = (self.investment_amount + self.paid_dividends.loc[dt]) / price

                # Update previous date
                _df_prev = dt


            

            


        #     # Compute dividends paid on ex-dividend date, based on stocks held on previous day


        

        # Return a detailed DataFrame, filtered to only show investment days.
        return pd.DataFrame(
            {
                "Stock Price": self.price_time_series[investment_mask],
                "Total Investments": invested_cash_cumsum[investment_mask],
                "Stocks Bought": stocks_bought[investment_mask],
                "Dividends Paid": paid_dividends[investment_mask],
                "Cumulative Stocks": cumulative_stocks[investment_mask],
                "Cumulative Dividends": cumulative_dividends[investment_mask],
                "Wealth": wealth[investment_mask],
            }
        )

    def invest(self, reinvest_dividends: bool) -> pd.DataFrame:
        """Execute the investment strategy with or without dividend reinvestment.

        This is the main method to run the investment simulation. It delegates
        to either the dividend reinvestment method or the standard projection
        method based on the reinvest_dividends parameter.

        Args:
            reinvest_dividends (bool): If True, dividends are reinvested to
                purchase additional shares. If False, dividends are received
                as cash and not reinvested.

        Returns:
            pd.DataFrame: Investment performance data with columns for dates,
                prices, investments, stocks, dividends, and wealth over time.
        """
        return (
            self.project_with_reinvested_dividends()
            if reinvest_dividends
            else self.project()
        )


class InvestDaily(AbstractInvest):
    """Investment strategy that invests every trading day.

    This strategy makes an investment on every available trading day in the
    price time series. It's the most frequent investment strategy available.
    """

    def _get_investment_mask(self):
        """Get investment mask for daily investments.

        Returns:
            pd.Series: Boolean series where all trading days are marked as
                investment days (all True values).
        """
        return pd.Series(True, index=self.price_time_series.index)


class InvestWeekly(AbstractInvest):
    """Investment strategy that invests weekly.

    This strategy makes an investment approximately once per week, starting
    from the first date in the price series and continuing at weekly intervals.
    The actual investment dates are the first available trading days on or
    after each target weekly anniversary.
    """

    def _get_investment_mask(self):
        """Get investment mask for weekly investments.

        Creates target dates at weekly intervals and finds the first available
        trading day on or after each target date.

        Returns:
            pd.Series: Boolean series where True indicates investment days
                occurring approximately weekly.
        """
        start_date = self.price_time_series.index[0]
        target_dates = pd.date_range(
            start=start_date,
            end=self.price_time_series.index[-1],
            freq=pd.DateOffset(weeks=1),
        )
        # Find the first available trading day on or after each target anniversary
        investment_indices = self.price_time_series.index.searchsorted(target_dates)
        # Ensure indices are within bounds
        investment_indices = investment_indices[
            investment_indices < len(self.price_time_series.index)
        ]
        investment_dates = self.price_time_series.index[investment_indices].unique()
        mask = self.price_time_series.index.isin(investment_dates)
        return pd.Series(mask, index=self.price_time_series.index)


class InvestMonthly(AbstractInvest):
    """Investment strategy that invests monthly.

    This strategy makes an investment approximately once per month, starting
    from the first date in the price series and continuing at monthly intervals.
    The actual investment dates are the first available trading days on or
    after each target monthly anniversary.
    """

    def _get_investment_mask(self):
        """Get investment mask for monthly investments.

        Creates target dates at monthly intervals and finds the first available
        trading day on or after each target date.

        Returns:
            pd.Series: Boolean series where True indicates investment days
                occurring approximately monthly.
        """
        start_date = self.price_time_series.index[0]
        target_dates = pd.date_range(
            start=start_date,
            end=self.price_time_series.index[-1],
            freq=pd.DateOffset(months=1),
        )
        investment_indices = self.price_time_series.index.searchsorted(target_dates)
        investment_indices = investment_indices[
            investment_indices < len(self.price_time_series.index)
        ]
        investment_dates = self.price_time_series.index[investment_indices].unique()
        mask = self.price_time_series.index.isin(investment_dates)
        return pd.Series(mask, index=self.price_time_series.index)


class InvestQuarterly(AbstractInvest):
    """Investment strategy that invests quarterly.

    This strategy makes an investment approximately once per quarter (every 3 months),
    starting from the first date in the price series and continuing at quarterly
    intervals. The actual investment dates are the first available trading days
    on or after each target quarterly anniversary.
    """

    def _get_investment_mask(self):
        """Get investment mask for quarterly investments.

        Creates target dates at 3-month intervals and finds the first available
        trading day on or after each target date.

        Returns:
            pd.Series: Boolean series where True indicates investment days
                occurring approximately quarterly.
        """
        start_date = self.price_time_series.index[0]
        target_dates = pd.date_range(
            start=start_date,
            end=self.price_time_series.index[-1],
            freq=pd.DateOffset(months=3),
        )
        investment_indices = self.price_time_series.index.searchsorted(target_dates)
        investment_indices = investment_indices[
            investment_indices < len(self.price_time_series.index)
        ]
        investment_dates = self.price_time_series.index[investment_indices].unique()
        mask = self.price_time_series.index.isin(investment_dates)
        return pd.Series(mask, index=self.price_time_series.index)


class InvestYearly(AbstractInvest):
    """Investment strategy that invests annually.

    This strategy makes an investment approximately once per year, starting
    from the first date in the price series and continuing at yearly intervals.
    The actual investment dates are the first available trading days on or
    after each target yearly anniversary.
    """

    def _get_investment_mask(self):
        """Get investment mask for yearly investments.

        Creates target dates at yearly intervals and finds the first available
        trading day on or after each target anniversary date.

        Returns:
            pd.Series: Boolean series where True indicates investment days
                occurring approximately annually.
        """
        start_date = self.price_time_series.index[0]
        # Generate target anniversary dates
        target_dates = pd.date_range(
            start=start_date,
            end=self.price_time_series.index[-1],
            freq=pd.DateOffset(years=1),
        )
        # Find the first available trading day on or after each target anniversary
        investment_indices = self.price_time_series.index.searchsorted(target_dates)
        # Ensure indices are within bounds
        investment_indices = investment_indices[
            investment_indices < len(self.price_time_series.index)
        ]
        # Get the actual investment dates from the price series index
        investment_dates = self.price_time_series.index[investment_indices].unique()
        mask = self.price_time_series.index.isin(investment_dates)
        return pd.Series(mask, index=self.price_time_series.index)
