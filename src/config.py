from datetime import datetime
from dateutil.relativedelta import relativedelta
from src.invest import (
    InvestDaily,
    InvestWeekly,
    InvestMonthly,
    InvestQuarterly,
    InvestYearly,
)


class Config:
    """
    Configuration class to hold all runtime parameters.
    Accepts optional config_dict for values from a TOML file.
    """

    _INVESTMENT_SCHEMA = {
        "daily": InvestDaily,
        "weekly": InvestWeekly,
        "monthly": InvestMonthly,
        "quarterly": InvestQuarterly,
        "yearly": InvestYearly,
    }
    _INTERVAL_MAP = {
        "daily": "Day",
        "weekly": "Week",
        "monthly": "Month",
        "quarterly": "Quarter",
        "yearly": "Year",
    }

    def __init__(self, config_dict: dict = None):
        """Initialize the Config object with runtime parameters.

        Loads configuration values from a dictionary (typically from a TOML file)
        and sets up all necessary parameters for investment analysis and animation.
        Missing values are filled with sensible defaults.

        Args:
            config_dict (dict, optional): Dictionary containing configuration values.
                Expected structure:
                - ticker (dict): Mapping of ticker symbols to company names
                - general (dict): General configuration with keys:
                    - years_ago (int): Number of years back to analyze
                    - investment_amount (int): Amount to invest per interval
                    - investment_kind (str): Investment frequency ("daily", "weekly", "monthly", "quarterly", "yearly")
                    - animation_duration_seconds (int): Duration of animation in seconds
                    - fps (int): Frames per second for animation
                    - tax_rate (float): Tax rate applied to dividends
                    - tax_free_return_threshold_per_annu (float): Annual tax-free dividend threshold
                - dividends (dict): Mapping of ticker symbols to boolean dividend reinvestment flags
                Defaults to None, which uses built-in defaults.

        Raises:
            ValueError: If investment_kind is not one of the supported values
                ("daily", "weekly", "monthly", "quarterly", "yearly").
        """
        config_dict = config_dict or {}
        # Get ticker config
        self.ticker: dict = config_dict.get("ticker", {"AAPL": "Apple"})
        # Get general config
        general_cfg = config_dict.get("general", {})
        self.years_ago: int = general_cfg.get("years_ago", 20)
        self.investment_amount: int = general_cfg.get("investment_amount", 1_000)
        self.investment_kind: str = general_cfg.get("investment_kind", "monthly")
        self.animation_duration_seconds: int = general_cfg.get(
            "animation_duration_seconds", 30
        )
        self.fps: int = general_cfg.get("fps", 28)
        self.tax_rate = general_cfg.get("tax_rate", 0.26375)
        self.tax_free_return_threshold_per_annu = general_cfg.get(
            "tax_free_return_threshold_per_annu", 1_000
        )
        self.save_data: bool = general_cfg.get("save_data", False)

        # Get dividends config
        self.dividends = config_dict.get("dividends", {})
        for ticker in self.ticker:
            if ticker not in self.dividends:
                self.dividends[ticker] = False

        # Derived fields
        self.interval: str = "1d"
        self.interval_title: str = self._INTERVAL_MAP[self.investment_kind]
        self.today: datetime = datetime.now()

        # Use relativedelta for a precise date calculation
        self.start_date: datetime = self.today - relativedelta(years=self.years_ago)

        self.today_str: str = self.today.strftime("%Y-%m-%d")
        self.start_date_str: str = self.start_date.strftime("%Y-%m-%d")

        if self.investment_kind not in self._INVESTMENT_SCHEMA:
            raise ValueError(f"Invalid investment_kind: '{self.investment_kind}'")
        self.investment_object = self._INVESTMENT_SCHEMA[self.investment_kind]
