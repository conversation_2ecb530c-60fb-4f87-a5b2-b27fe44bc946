#!/usr/bin/env python3
"""
Test script for the new strategy-based configuration system.

This script demonstrates how to use the new strategy system to compare
different investment approaches for the same ticker.
"""

import sys
from pathlib import Path
import tomllib

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config, InvestmentStrategy
from src.logger import get_logger


def test_strategy_parsing():
    """Test the strategy parsing functionality."""
    logger = get_logger(__name__)
    logger.activate()
    
    print("🧪 Testing strategy-based configuration...")
    print("=" * 50)
    
    # Test with strategy configuration
    strategy_config = {
        "general": {
            "years_ago": 5,
            "investment_amount": 1000,
            "investment_kind": "monthly"
        },
        "strategies": {
            "AAPL_no_reinvest": {
                "ticker": "AAPL",
                "name": "Apple (No Reinvestment)",
                "reinvest_dividends": False,
                "timing": "regular"
            },
            "AAPL_reinvest": {
                "ticker": "AAPL", 
                "name": "Apple (With Reinvestment)",
                "reinvest_dividends": True,
                "timing": "regular"
            },
            "MSFT_no_reinvest": {
                "ticker": "MSFT",
                "name": "Microsoft (No Reinvestment)", 
                "reinvest_dividends": False,
                "timing": "regular"
            }
        }
    }
    
    config = Config(config_dict=strategy_config)
    
    print(f"📊 Parsed {len(config.strategies)} strategies:")
    for strategy in config.strategies:
        print(f"  • {strategy.strategy_id}: {strategy.display_name}")
        print(f"    - Ticker: {strategy.ticker_symbol}")
        print(f"    - Reinvest dividends: {strategy.reinvest_dividends}")
        print(f"    - Timing: {strategy.investment_timing}")
    
    # Test backward compatibility with old format
    print(f"\n🔄 Testing backward compatibility...")
    old_config = {
        "general": {
            "years_ago": 5,
            "investment_amount": 1000,
            "investment_kind": "monthly"
        },
        "ticker": {
            "AAPL": "Apple",
            "MSFT": "Microsoft"
        },
        "dividends": {
            "AAPL": False,
            "MSFT": True
        }
    }
    
    old_format_config = Config(config_dict=old_config)
    
    print(f"📊 Parsed {len(old_format_config.strategies)} strategies from old format:")
    for strategy in old_format_config.strategies:
        print(f"  • {strategy.strategy_id}: {strategy.display_name}")
        print(f"    - Ticker: {strategy.ticker_symbol}")
        print(f"    - Reinvest dividends: {strategy.reinvest_dividends}")
    
    # Test loading from example file
    print(f"\n📁 Testing example configuration file...")
    try:
        with open("config_strategies_example.toml", "rb") as f:
            example_config_data = tomllib.load(f)
        
        example_config = Config(config_dict=example_config_data)
        
        print(f"📊 Loaded {len(example_config.strategies)} strategies from example file:")
        for strategy in example_config.strategies:
            print(f"  • {strategy.strategy_id}: {strategy.display_name}")
            print(f"    - Ticker: {strategy.ticker_symbol}")
            print(f"    - Reinvest dividends: {strategy.reinvest_dividends}")
        
    except FileNotFoundError:
        print("⚠️  Example config file not found, skipping...")
    
    print(f"\n✅ Strategy system test completed!")
    
    return config


def demonstrate_same_ticker_strategies():
    """Demonstrate how to create multiple strategies for the same ticker."""
    print(f"\n🎯 Demonstrating same-ticker strategies...")
    print("=" * 50)
    
    # Create strategies for the same ticker with different settings
    strategies = [
        InvestmentStrategy(
            strategy_id="AAPL_conservative",
            ticker_symbol="AAPL",
            display_name="Apple (Conservative - No Reinvestment)",
            reinvest_dividends=False,
            investment_timing="regular"
        ),
        InvestmentStrategy(
            strategy_id="AAPL_aggressive", 
            ticker_symbol="AAPL",
            display_name="Apple (Aggressive - With Reinvestment)",
            reinvest_dividends=True,
            investment_timing="regular"
        ),
        InvestmentStrategy(
            strategy_id="AAPL_peaks",
            ticker_symbol="AAPL", 
            display_name="Apple (Buy at Peaks)",
            reinvest_dividends=False,
            investment_timing="peaks"
        ),
        InvestmentStrategy(
            strategy_id="AAPL_lows",
            ticker_symbol="AAPL",
            display_name="Apple (Buy at Lows)", 
            reinvest_dividends=False,
            investment_timing="lows"
        )
    ]
    
    print("🍎 Apple strategies comparison:")
    for strategy in strategies:
        print(f"  • {strategy.display_name}")
        print(f"    - ID: {strategy.strategy_id}")
        print(f"    - Reinvest: {strategy.reinvest_dividends}")
        print(f"    - Timing: {strategy.investment_timing}")
    
    print(f"\n💡 This allows you to compare:")
    print("  - Impact of dividend reinvestment")
    print("  - Different investment timing strategies")
    print("  - Multiple approaches for the same stock")
    
    return strategies


if __name__ == "__main__":
    try:
        test_strategy_parsing()
        demonstrate_same_ticker_strategies()
        print("\n🎉 All strategy tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
