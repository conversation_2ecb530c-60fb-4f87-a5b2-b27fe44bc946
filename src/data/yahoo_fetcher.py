import yfinance as yf
import pandas as pd

from src.logger import get_logger
from src.utils.paths import get_data_dir, get_safe_filename


LOGGER = get_logger(__name__)  # Initialize the logger


def get_stock_price_timeseries(
    ticker_symbol: str,
    period: str = None,
    interval: str = "1d",
    start_date: str = None,
    end_date: str = None,
    save_data: bool = False,
) -> pd.DataFrame:
    """Fetch stock price data for a given ticker symbol using Yahoo Finance.

    Retrieves historical stock data from Yahoo Finance API. When both period
    and start_date/end_date are provided, the date range takes precedence.

    Args:
        ticker_symbol (str): The stock ticker symbol (e.g., "AAPL", "MSFT").
        period (str, optional): The period for which to fetch data. Valid values
            include "1d", "5d", "1mo", "3mo", "1y", "ytd", "max". Used only if
            start_date and end_date are not provided. For intraday data
            (interval < 1d), period must be <= 7 days. Defaults to None.
        interval (str, optional): The data interval. Valid values include "1m",
            "2m", "5m", "15m", "30m", "60m", "90m", "1h", "1d", "5d", "1wk",
            "1mo", "3mo". Note: 1m data is only available for the last 7 days.
            Defaults to "1d".
        start_date (str, optional): The start date in "YYYY-MM-DD" format.
            Takes precedence over period. Defaults to None.
        end_date (str, optional): The end date in "YYYY-MM-DD" format.
            Takes precedence over period. Defaults to None.
        save_data (bool, optional): Whether to save the fetched data to a CSV file
            in the project's data directory. Defaults to False.

    Returns:
        pd.DataFrame: DataFrame containing historical stock data with columns:
            Open, High, Low, Close, Adj Close, Volume, Dividends, Stock Splits.
            Returns an empty DataFrame if data cannot be fetched or ticker is invalid.

    Raises:
        Exception: Logs any exceptions that occur during data fetching but
            returns an empty DataFrame instead of raising.
    """

    LOGGER.activate()  # Assuming this activates logging for the current call

    LOGGER.info(f" --- Starting to fetch timeseries data for {ticker_symbol!r}.")

    try:
        ticker = yf.Ticker(ticker_symbol)

        # Determine how to fetch data: by start/end dates or by period
        if start_date and end_date:
            LOGGER.info(
                f" --- Fetching data for {ticker_symbol} from {start_date} to {end_date} with interval '{interval}'."
            )
            data = ticker.history(start=start_date, end=end_date, interval=interval)
        elif period:
            # Handle specific period limitations for '1m' interval
            if interval == "1m":
                if period not in [
                    "1d",
                    "5d",
                    "7d",
                ]:  # yfinance often treats "7d" as "5d" implicitly
                    LOGGER.warning(
                        f" --- For '1m' interval, period '{period}' is too long. Adjusting period to '5d' for {ticker_symbol}."
                    )
                    period = "5d"
            LOGGER.info(
                f" --- Fetching data for {ticker_symbol} for period '{period}' with interval '{interval}'."
            )
            data = ticker.history(period=period, interval=interval)
        else:
            LOGGER.warning(
                " --- Neither 'period' nor 'start_date'/'end_date' were provided. Fetching data for default period '1d'."
            )
            data = ticker.history(period="1d", interval=interval)

        if data.empty:
            LOGGER.warning(
                f" --- No data found for {ticker_symbol} with the specified parameters."
            )

        # Convert index to datetime
        data.index = pd.to_datetime(data.index, format="%Y-%m-%d", utc=True)

        if save_data:
            data_dir = get_data_dir()
            filename = get_safe_filename(
                ticker_symbol,
                start_date or "None",
                end_date or "None",
                interval,
                period,
            )
            file_path = data_dir / filename
            data.to_csv(file_path, index="Date")
            LOGGER.info(f" --- Saving data for {ticker_symbol} to CSV: {file_path}")

        return data

    except Exception as e:
        LOGGER.error(
            f" --- An error occurred while fetching data for {ticker_symbol}: {e}"
        )
        return pd.DataFrame()
